"""
Reproductive Health Central Router Tool
Determines which reproductive health tool to use based on user profile and query
"""

import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime, date

logger = logging.getLogger(__name__)

class ReproductiveCentralRouter:
    """Central router for reproductive health tools"""
    
    def __init__(self):
        self.tool_mapping = {
            "menstrual": "reproductive_health",
            "cycle": "reproductive_health", 
            "ovulation": "reproductive_health",
            "period": "reproductive_health",
            "fertility": "reproductive_health",
            "pregnancy": "pregnancy_monitoring",
            "pregnant": "pregnancy_monitoring",
            "gestational": "pregnancy_monitoring",
            "contraction": "pregnancy_monitoring",
            "kick": "pregnancy_monitoring",
            "fetal": "pregnancy_monitoring",
            "postpartum": "postpartum_tracker",
            "breastfeeding": "postpartum_tracker",
            "recovery": "postpartum_tracker",
            "baby": "postpartum_tracker",
            "delivery": "postpartum_tracker"
        }
    
    def determine_tool(self, query: str, user_profile: Optional[Dict] = None) -> str:
        """Determine which reproductive health tool to use"""
        query_lower = query.lower()
        
        # Check user profile first if available
        if user_profile:
            if user_profile.get("is_pregnant"):
                return "pregnancy_monitoring"
            elif user_profile.get("is_postpartum"):
                return "postpartum_tracker"
            elif user_profile.get("tracking_menstrual_cycle"):
                return "reproductive_health"
        
        # Check query keywords
        for keyword, tool in self.tool_mapping.items():
            if keyword in query_lower:
                return tool
        
        # Default to general reproductive health
        return "reproductive_health"
    
    def route_query(self, query: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Route query to appropriate reproductive health tool"""
        try:
            user_profile = user_data.get("profile", {})
            tool_name = self.determine_tool(query, user_profile)
            
            response = {
                "recommended_tool": tool_name,
                "query": query,
                "routing_reason": self._get_routing_reason(tool_name, query, user_profile)
            }
            
            # Add tool-specific guidance
            if tool_name == "reproductive_health":
                response["guidance"] = "I can help you track your menstrual cycle, predict ovulation, and log reproductive health activities."
            elif tool_name == "pregnancy_monitoring":
                response["guidance"] = "I can help you monitor your pregnancy, track symptoms, time contractions, and count fetal movements."
            elif tool_name == "postpartum_tracker":
                response["guidance"] = "I can help you track your postpartum recovery, monitor your baby's development, and manage breastfeeding."
            
            return response
            
        except Exception as e:
            logger.error(f"Error in reproductive central router: {str(e)}")
            return {
                "error": f"Failed to route reproductive health query: {str(e)}",
                "recommended_tool": "reproductive_health"  # Default fallback
            }
    
    def _get_routing_reason(self, tool_name: str, query: str, user_profile: Dict) -> str:
        """Get explanation for why this tool was chosen"""
        if user_profile.get("is_pregnant"):
            return "Routed to pregnancy monitoring based on user profile"
        elif user_profile.get("is_postpartum"):
            return "Routed to postpartum tracking based on user profile"
        elif any(keyword in query.lower() for keyword in ["pregnancy", "pregnant", "gestational"]):
            return "Routed to pregnancy monitoring based on query keywords"
        elif any(keyword in query.lower() for keyword in ["postpartum", "breastfeeding", "recovery", "baby"]):
            return "Routed to postpartum tracking based on query keywords"
        else:
            return "Routed to general reproductive health tracking"

def reproductive_central_router(data: str) -> str:
    """Main function for reproductive health central routing"""
    try:
        # Parse input data
        input_data = json.loads(data)
        query = input_data.get("query", "")
        user_data = input_data.get("user_data", {})
        
        # Initialize router
        router = ReproductiveCentralRouter()
        
        # Route the query
        result = router.route_query(query, user_data)
        
        return json.dumps(result, indent=2)
        
    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error in reproductive central router: {str(e)}")
        return json.dumps({
            "error": "Invalid JSON input",
            "recommended_tool": "reproductive_health"
        })
    except Exception as e:
        logger.error(f"Error in reproductive central router: {str(e)}")
        return json.dumps({
            "error": f"Failed to process reproductive health routing: {str(e)}",
            "recommended_tool": "reproductive_health"
        })

# Tool metadata for agent integration
TOOL_METADATA = {
    "name": "reproductive_central_router",
    "description": "Routes reproductive health queries to the appropriate specialized tool",
    "parameters": {
        "query": "User's reproductive health query",
        "user_data": "User profile and health data"
    }
}
