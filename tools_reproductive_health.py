import json
import pandas as pd
import numpy as np
import datetime
from typing import Dict, Any
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from statsmodels.tsa.arima.model import ARIMA

CYCLE_FILE = "user_data.json"
ACTIVITY_FILE = "activity_data.json"
POSTPARTUM_LOG = "postpartum_logs.json"

def load_json(file):
    try:
        with open(file, "r") as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_json(file, data):
    with open(file, "w") as f:
        json.dump(data, f, indent=4)

def add_cycle_data(user, payload):
    data = load_json(CYCLE_FILE)
    cycles = data.setdefault(user, {}).setdefault("cycle_data", [])
    new_entry = {
        "start_date": payload["start_date"],
        "period_duration": payload["period_duration"],
        "luteal_phase": payload["luteal_phase"],
        "stress": payload["stress"],
        "exercise": payload["exercise"],
        "sleep": payload["sleep"],
        "weight_change": payload["weight_change"]
    }
    cycles.append(new_entry)
    data[user]["cycle_data"] = sort_and_recalculate_cycles(cycles)
    save_json(CYCLE_FILE, data)
    return data[user]["cycle_data"]

def sort_and_recalculate_cycles(cycles):
    df = pd.DataFrame(cycles)
    df["start_date"] = pd.to_datetime(df["start_date"])
    df = df.sort_values("start_date")
    cycle_lengths = [28]
    for i in range(1, len(df)):
        cycle_lengths.append((df.iloc[i]["start_date"] - df.iloc[i - 1]["start_date"]).days)
    df["cycle_length"] = cycle_lengths
    df["end_date"] = df["start_date"] + pd.to_timedelta(df["period_duration"], unit='D')
    df["start_date"] = df["start_date"].dt.strftime('%Y-%m-%d')
    df["end_date"] = df["end_date"].dt.strftime('%Y-%m-%d')
    return df.to_dict(orient="records")

def predict_next_cycle(user):
    data = load_json(CYCLE_FILE).get(user, {}).get("cycle_data", [])
    if len(data) < 3:
        return {"warning": "Not enough data for prediction"}
    df = pd.DataFrame(data)
    df["start_date"] = pd.to_datetime(df["start_date"])
    df = df.sort_values("start_date")
    ts = df["cycle_length"].astype(float)
    train = ts[:-1]
    last = df.iloc[-1]["start_date"]

    try:
        if len(train) < 3 or train.nunique() == 1:
            next_cycle_len = round(train.mean())
            model_type = "mean"
        else:
            model = ARIMA(train, order=(1, 1, 1)).fit()
            forecast = model.forecast()
            next_cycle_len = round(forecast[0] if isinstance(forecast, (np.ndarray, list, pd.Series)) else float(forecast))
            model_type = "arima"
    except Exception:
        next_cycle_len = round(train.mean())
        model_type = "mean (fallback)"

    next_start = last + pd.Timedelta(days=next_cycle_len)
    ovulation = next_start - pd.Timedelta(days=14)
    window = f"{(ovulation - pd.Timedelta(days=2)).strftime('%Y-%m-%d')} to {(ovulation + pd.Timedelta(days=2)).strftime('%Y-%m-%d')}"

    return {
        "Predicted Cycle Length": next_cycle_len,
        "Prediction Method": model_type,
        "Next Period Start": next_start.strftime('%Y-%m-%d'),
        "Ovulation Window": window
    }

def calculate_gestational_age(lmp_date):
    today = datetime.date.today()
    delta = (today - lmp_date).days
    return delta // 7, delta % 7

def predict_diagnosis(symptoms, weeks):
    trimester = "First" if weeks <= 12 else "Second" if weeks <= 27 else "Third"
    diagnosis = []
    if trimester == "First":
        if "Light spotting (pink or brown)" in symptoms:
            diagnosis.append("Implantation Bleeding (🟢 Normal)")
        if "Moderate cramps or back pain with bleeding" in symptoms:
            diagnosis.append("Possible Threatened Miscarriage (🟠 Caution)")
        if "Heavy bleeding with clots + strong cramps" in symptoms:
            diagnosis.append("Miscarriage Risk (🔴 Alert)")
    if trimester == "Second":
        if "Painless, bright red bleeding" in symptoms:
            diagnosis.append("Placenta Previa (🔴 High Risk)")
    if trimester == "Third":
        if "Severe headaches + swelling or vision changes" in symptoms:
            diagnosis.append("Possible Preeclampsia (🔴 Critical)")
    return diagnosis or ["No critical symptoms detected"]

def expected_delivery(lmp_date):
    return {
        "Start": (lmp_date + datetime.timedelta(weeks=37)).strftime('%Y-%m-%d'),
        "End": (lmp_date + datetime.timedelta(weeks=42)).strftime('%Y-%m-%d')
    }

def detect_anomalies(entry):
    anomalies = []
    if entry.get("mood") in ["sad", "anxious"] and entry.get("sleep_hours", 0) < 4:
        anomalies.append("🧠 Mother is low on mood with < 4h sleep")
    if entry.get("pain_level", 0) >= 6:
        anomalies.append("💥 High pain level after delivery")
    if entry.get("feeding_frequency", 0) < 6:
        anomalies.append("🍼 Baby feeding frequency low")
    if not entry.get("urinates", True):
        anomalies.append("🚨 Baby has not urinated today")
    if "redness" in entry.get("wound_notes", "").lower():
        anomalies.append("🩸 Wound shows redness — infection risk")
    return anomalies

def track_postpartum_cycle(breastfeeding_months):
    return f"🕒 Ovulation may delay by approx. {breastfeeding_months * 0.5:.1f} months"

def run_reproductive_agent(user_id: str, mode: str, payload: Dict[str, Any]) -> Dict[str, Any]:
    if mode == "cycle":
        cycles = add_cycle_data(user_id, payload)
        prediction = predict_next_cycle(user_id)
        recommendations = [
            "✅ You're maintaining healthy habits — keep up the good work!",
            "💤 Ensure you continue with good sleep patterns for hormonal regulation.",
            "🏃‍♀️ Light exercise is great — try including yoga or brisk walks during your follicular phase.",
            "💧 Stay hydrated and eat iron-rich foods post-period."
        ]
        return {
            "mode": "Cycle Tracking",
            "latest_cycle": cycles[-1],
            "next_prediction": prediction,
            "recommendations": recommendations,
            "offer_chat": True,
            "chat_prompt": "Would you like to chat with Dr. Deuce for deeper insights and support on your cycle health?"
        }

    elif mode == "pregnancy":
        lmp = datetime.datetime.strptime(payload["lmp_date"], "%Y-%m-%d").date()
        weeks, days = calculate_gestational_age(lmp)
        diag = predict_diagnosis(payload.get("symptoms", []), weeks)
        edd = expected_delivery(lmp)

        recommendations = [
            f"👶 You are {weeks} weeks and {days} days pregnant. Attend regular prenatal checkups.",
            "💊 Take your prenatal vitamins daily (folic acid, iron, DHA).",
            "🥗 Eat balanced meals — include leafy greens, lean protein, and whole grains.",
            "🚰 Hydrate with at least 2–3 liters of water daily.",
            "🛌 Rest well. Try gentle stretches or prenatal yoga to ease discomfort."
        ]

        if "Placenta Previa" in diag[0] or "Preeclampsia" in diag[0]:
            recommendations.append("⚠️ These symptoms require urgent medical review — please consult your doctor.")

        return {
            "mode": "Pregnancy Monitoring",
            "Gestational Age": f"{weeks} weeks {days} days",
            "Expected Delivery Window": edd,
            "Diagnosis": diag,
            "recommendations": recommendations,
            "offer_chat": True,
            "chat_prompt": "Would you like to chat with Dr. Deuce for more personalized pregnancy tips and support?"
        }

    elif mode == "postpartum":
        delivery = datetime.datetime.strptime(payload["delivery_date"], "%Y-%m-%d").date()
        days = (datetime.date.today() - delivery).days
        anomalies = detect_anomalies(payload)
        ovulation = track_postpartum_cycle(payload["breastfeeding_duration"])

        recommendations = [
            "🧼 Keep incision area clean and dry.",
            "💊 Continue post-op meds as prescribed unless otherwise advised.",
            "🛌 Allow your body adequate rest; limit heavy lifting.",
            "🍼 Baby feeding should be every 2–3 hours; track urine and stool daily.",
            "👩‍⚕️ Reach out if you feel persistently overwhelmed, sad, or in pain."
        ]

        for flag in anomalies:
            if "urinated" in flag:
                recommendations.append("🚨 Urine absence may indicate dehydration — seek medical help.")
            if "infection" in flag:
                recommendations.append("🩹 Redness around wound site — check with your doctor immediately.")
            if "low on mood" in flag:
                recommendations.append("🧠 Low mood and poor sleep may be signs of postpartum depression. Talk to a counselor or support group.")

        return {
            "mode": "Postpartum Recovery",
            "Days Since Delivery": days,
            "Flags": anomalies,
            "Ovulation Info": ovulation,
            "recommendations": recommendations,
            "offer_chat": True,
            "chat_prompt": "Would you like to chat with Dr. Deuce for postpartum recovery advice or baby care tips?"
        }

    return {
        "error": "Invalid mode selected.",
        "offer_chat": False,
        "chat_prompt": "Unable to determine mode. Please check your input."
    }
