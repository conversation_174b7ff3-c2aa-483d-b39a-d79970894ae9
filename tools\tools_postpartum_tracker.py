"""
Postpartum Tracker Tool
Handles postpartum recovery monitoring, baby care tracking, and breastfeeding support
"""

import json
import logging
import datetime
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class PostpartumTrackerTool:
    """Tool for comprehensive postpartum recovery and baby care tracking"""
    
    def __init__(self):
        self.vaccination_schedule = {
            6: "First DTP, IPV, Hib, and HepB vaccine due",
            10: "Second DTP, IPV, Hib, and HepB vaccine due",
            14: "Third DTP, IPV, Hib, and HepB vaccine due",
            24: "First MMR, Varicella vaccine due",
            36: "Second MMR, Varicella vaccine due"
        }
    
    def track_recovery(self, recovery_data: Dict) -> Dict[str, Any]:
        """Track maternal postpartum recovery"""
        try:
            delivery_date = datetime.datetime.strptime(recovery_data.get("delivery_date"), "%Y-%m-%d").date()
            today = datetime.date.today()
            days_since_delivery = (today - delivery_date).days
            weeks_since_delivery = days_since_delivery // 7
            
            mother_data = recovery_data.get("mother", {})
            
            # Analyze recovery metrics
            recovery_analysis = {
                "days_postpartum": days_since_delivery,
                "weeks_postpartum": weeks_since_delivery,
                "delivery_type": recovery_data.get("delivery_type", "Unknown"),
                "recovery_stage": self._determine_recovery_stage(weeks_since_delivery),
                "alerts": self._detect_recovery_alerts(mother_data, days_since_delivery),
                "recommendations": self._get_recovery_recommendations(mother_data, recovery_data.get("delivery_type"))
            }
            
            # Add specific recovery metrics
            recovery_analysis.update({
                "sleep_quality": self._assess_sleep_quality(mother_data.get("sleep_hours", 0)),
                "pain_assessment": self._assess_pain_level(mother_data.get("pain_level", 0), days_since_delivery),
                "mood_assessment": self._assess_mood(mother_data.get("mood"), mother_data.get("mood_log", [])),
                "wound_status": self._assess_wound_healing(mother_data.get("wound_data", {}), recovery_data.get("delivery_type"))
            })
            
            return recovery_analysis
            
        except ValueError as e:
            logger.error(f"Invalid date format: {e}")
            return {"error": "Invalid date format. Please use YYYY-MM-DD"}
        except Exception as e:
            logger.error(f"Error tracking recovery: {e}")
            return {"error": f"Failed to track recovery: {str(e)}"}
    
    def track_baby_development(self, baby_data: Dict, weeks_since_birth: int) -> Dict[str, Any]:
        """Track baby development and provide guidance"""
        try:
            development_analysis = {
                "weeks_old": weeks_since_birth,
                "development_stage": self._determine_baby_stage(weeks_since_birth),
                "feeding_assessment": self._assess_feeding(baby_data),
                "sleep_assessment": self._assess_baby_sleep(baby_data.get("sleep_hours", 0), weeks_since_birth),
                "health_alerts": self._detect_baby_alerts(baby_data),
                "developmental_milestones": self._get_developmental_milestones(weeks_since_birth),
                "next_vaccination": self._get_next_vaccination(weeks_since_birth),
                "recommendations": self._get_baby_recommendations(weeks_since_birth, baby_data)
            }
            
            return development_analysis
            
        except Exception as e:
            logger.error(f"Error tracking baby development: {e}")
            return {"error": f"Failed to track baby development: {str(e)}"}
    
    def track_breastfeeding(self, feeding_data: Dict) -> Dict[str, Any]:
        """Track breastfeeding progress and provide support"""
        try:
            weeks_since_birth = feeding_data.get("weeks_since_birth", 0)
            
            breastfeeding_analysis = {
                "feeding_frequency": feeding_data.get("feeding_frequency", 0),
                "feeding_assessment": self._assess_breastfeeding_frequency(feeding_data.get("feeding_frequency", 0), weeks_since_birth),
                "nutrition_recommendations": self._get_breastfeeding_nutrition_advice(weeks_since_birth),
                "common_challenges": self._get_breastfeeding_challenges(weeks_since_birth),
                "support_resources": self._get_breastfeeding_support(),
                "ovulation_impact": self._calculate_ovulation_delay(feeding_data.get("breastfeeding_duration_months", 0))
            }
            
            # Add specific feeding concerns
            if feeding_data.get("feeding_difficulties"):
                breastfeeding_analysis["difficulty_support"] = self._address_feeding_difficulties(feeding_data.get("feeding_difficulties"))
            
            return breastfeeding_analysis
            
        except Exception as e:
            logger.error(f"Error tracking breastfeeding: {e}")
            return {"error": f"Failed to track breastfeeding: {str(e)}"}
    
    def _determine_recovery_stage(self, weeks: int) -> str:
        """Determine postpartum recovery stage"""
        if weeks < 2:
            return "Immediate postpartum (0-2 weeks)"
        elif weeks < 6:
            return "Early postpartum (2-6 weeks)"
        elif weeks < 12:
            return "Extended postpartum (6-12 weeks)"
        else:
            return "Late postpartum (3+ months)"
    
    def _detect_recovery_alerts(self, mother_data: Dict, days_since_delivery: int) -> List[str]:
        """Detect potential recovery issues"""
        alerts = []
        
        # Sleep and mood alerts
        if mother_data.get("mood") in ["sad", "anxious"] and mother_data.get("sleep_hours", 0) < 4:
            alerts.append("⚠️ Low mood with insufficient sleep - consider postpartum depression screening")
        
        # Pain level alerts
        if mother_data.get("pain_level", 0) >= 6 and days_since_delivery > 14:
            alerts.append("⚠️ High pain level after 2 weeks - consult healthcare provider")
        
        # Wound healing alerts
        wound_notes = mother_data.get("wound_notes", "").lower()
        if any(concern in wound_notes for concern in ["redness", "discharge", "swelling", "fever"]):
            alerts.append("⚠️ Potential wound infection signs - seek medical attention")
        
        # Mood pattern alerts
        mood_log = mother_data.get("mood_log", [])
        if mood_log.count("sad") + mood_log.count("anxious") >= 3:
            alerts.append("⚠️ Consistent low mood pattern - consider professional support")
        
        return alerts
    
    def _detect_baby_alerts(self, baby_data: Dict) -> List[str]:
        """Detect potential baby health concerns"""
        alerts = []
        
        # Feeding alerts
        if baby_data.get("feeding_frequency", 0) < 6:
            alerts.append("⚠️ Low feeding frequency - monitor baby's weight and hydration")
        
        # Urination alerts
        if not baby_data.get("urinates", True):
            alerts.append("⚠️ No urination recorded today - ensure adequate feeding and hydration")
        
        # Stool color alerts
        stool_color = baby_data.get("stool_color", "").lower()
        if stool_color == "black" and baby_data.get("age_days", 0) > 3:
            alerts.append("⚠️ Black stool after first few days - consult pediatrician")
        elif stool_color == "white" or stool_color == "clay":
            alerts.append("⚠️ Unusual stool color - consult pediatrician immediately")
        
        return alerts
    
    def _assess_sleep_quality(self, sleep_hours: int) -> Dict[str, str]:
        """Assess maternal sleep quality"""
        if sleep_hours < 4:
            return {"status": "Poor", "recommendation": "Prioritize rest when baby sleeps, consider support for night feeds"}
        elif sleep_hours < 6:
            return {"status": "Fair", "recommendation": "Try to increase rest periods, share night duties if possible"}
        else:
            return {"status": "Good", "recommendation": "Maintain current sleep routine"}
    
    def _assess_pain_level(self, pain_level: int, days_since_delivery: int) -> Dict[str, str]:
        """Assess pain level appropriateness"""
        if days_since_delivery <= 7:
            if pain_level <= 4:
                return {"status": "Normal", "recommendation": "Pain level appropriate for early recovery"}
            else:
                return {"status": "High", "recommendation": "Consider pain management options, consult healthcare provider"}
        elif days_since_delivery <= 14:
            if pain_level <= 3:
                return {"status": "Normal", "recommendation": "Pain decreasing as expected"}
            else:
                return {"status": "Concerning", "recommendation": "Pain should be decreasing - consult healthcare provider"}
        else:
            if pain_level <= 2:
                return {"status": "Normal", "recommendation": "Recovery progressing well"}
            else:
                return {"status": "Concerning", "recommendation": "Persistent pain after 2 weeks requires medical evaluation"}
    
    def _assess_mood(self, current_mood: str, mood_log: List[str]) -> Dict[str, str]:
        """Assess maternal mood and mental health"""
        concerning_moods = ["sad", "anxious", "overwhelmed", "tearful"]
        
        if current_mood in concerning_moods:
            if len([m for m in mood_log if m in concerning_moods]) >= 3:
                return {
                    "status": "Concerning Pattern", 
                    "recommendation": "Consider postpartum depression screening and professional support"
                }
            else:
                return {
                    "status": "Monitor", 
                    "recommendation": "Normal adjustment period, continue monitoring mood changes"
                }
        else:
            return {"status": "Stable", "recommendation": "Mood appears stable, continue self-care practices"}
    
    def _assess_wound_healing(self, wound_data: Dict, delivery_type: str) -> Dict[str, str]:
        """Assess wound healing progress"""
        if delivery_type.lower() == "cesarean":
            if wound_data.get("fever_present") == "Yes" or wound_data.get("incision_appearance") == "Severe":
                return {"status": "Concerning", "recommendation": "Possible infection - seek immediate medical attention"}
            elif wound_data.get("incision_appearance") == "Mild":
                return {"status": "Monitor", "recommendation": "Some irritation normal, keep clean and dry"}
            else:
                return {"status": "Healing Well", "recommendation": "Continue current wound care routine"}
        else:  # Vaginal delivery
            if wound_data.get("discharge") == "Yes" or wound_data.get("perineal_healing") == "Severe":
                return {"status": "Concerning", "recommendation": "Unusual symptoms - consult healthcare provider"}
            elif wound_data.get("perineal_healing") == "Mild":
                return {"status": "Normal Healing", "recommendation": "Use sitz baths and proper hygiene"}
            else:
                return {"status": "Healing Well", "recommendation": "Recovery progressing normally"}
    
    def _determine_baby_stage(self, weeks: int) -> str:
        """Determine baby developmental stage"""
        if weeks < 4:
            return "Newborn (0-4 weeks)"
        elif weeks < 8:
            return "Young infant (1-2 months)"
        elif weeks < 16:
            return "Infant (2-4 months)"
        elif weeks < 24:
            return "Older infant (4-6 months)"
        else:
            return "Mobile infant (6+ months)"
    
    def _assess_feeding(self, baby_data: Dict) -> Dict[str, str]:
        """Assess baby feeding patterns"""
        frequency = baby_data.get("feeding_frequency", 0)
        
        if frequency < 6:
            return {"status": "Low", "recommendation": "Increase feeding frequency, monitor weight gain"}
        elif frequency <= 12:
            return {"status": "Normal", "recommendation": "Feeding frequency within normal range"}
        else:
            return {"status": "High", "recommendation": "Very frequent feeding - ensure proper latch and milk supply"}
    
    def _assess_baby_sleep(self, sleep_hours: int, weeks: int) -> Dict[str, str]:
        """Assess baby sleep patterns"""
        if weeks < 4:
            expected_range = "14-17 hours"
            if sleep_hours < 12:
                return {"status": "Low", "recommendation": "Newborns need more sleep - check feeding and comfort"}
        elif weeks < 12:
            expected_range = "12-15 hours"
            if sleep_hours < 10:
                return {"status": "Low", "recommendation": "May need sleep routine adjustments"}
        else:
            expected_range = "11-14 hours"
            if sleep_hours < 9:
                return {"status": "Low", "recommendation": "Consider sleep training methods"}
        
        return {"status": "Normal", "expected_range": expected_range}
    
    def _get_developmental_milestones(self, weeks: int) -> List[str]:
        """Get expected developmental milestones"""
        if weeks < 4:
            return ["Focuses on faces", "Responds to loud sounds", "Moves head from side to side"]
        elif weeks < 8:
            return ["Smiles responsively", "Follows objects with eyes", "Holds head up briefly"]
        elif weeks < 16:
            return ["Laughs and coos", "Holds head steady", "Brings hands to mouth"]
        elif weeks < 24:
            return ["Rolls over", "Sits with support", "Reaches for objects"]
        else:
            return ["Sits without support", "Crawls or scoots", "Responds to name"]
    
    def _get_next_vaccination(self, weeks: int) -> Optional[str]:
        """Get next vaccination due"""
        return self.vaccination_schedule.get(weeks)
    
    def _get_recovery_recommendations(self, mother_data: Dict, delivery_type: str) -> List[str]:
        """Get personalized recovery recommendations"""
        recommendations = []
        
        # General recommendations
        recommendations.append("Rest when baby sleeps")
        recommendations.append("Stay hydrated and eat nutritious meals")
        recommendations.append("Accept help from family and friends")
        
        # Delivery-specific recommendations
        if delivery_type and delivery_type.lower() == "cesarean":
            recommendations.extend([
                "Avoid lifting anything heavier than your baby",
                "Keep incision clean and dry",
                "Take prescribed pain medication as needed"
            ])
        else:
            recommendations.extend([
                "Use sitz baths for perineal comfort",
                "Practice Kegel exercises when comfortable",
                "Use ice packs for swelling relief"
            ])
        
        # Mood-specific recommendations
        if mother_data.get("mood") in ["sad", "anxious"]:
            recommendations.append("Consider joining a new parent support group")
        
        return recommendations
    
    def _get_baby_recommendations(self, weeks: int, baby_data: Dict) -> List[str]:
        """Get baby care recommendations"""
        recommendations = []
        
        if weeks < 4:
            recommendations.extend([
                "Feed on demand (8-12 times per day)",
                "Practice skin-to-skin contact",
                "Ensure safe sleep environment"
            ])
        elif weeks < 12:
            recommendations.extend([
                "Continue frequent feeding",
                "Start tummy time when awake",
                "Establish bedtime routine"
            ])
        else:
            recommendations.extend([
                "Introduce solid foods around 6 months",
                "Encourage interactive play",
                "Baby-proof living spaces"
            ])
        
        return recommendations
    
    def _get_breastfeeding_nutrition_advice(self, weeks: int) -> str:
        """Get breastfeeding nutrition recommendations"""
        if weeks < 6:
            return "Exclusive breastfeeding recommended for first 6 months"
        elif weeks < 24:
            return "Continue breastfeeding while introducing solid foods"
        else:
            return "Breastfeeding can continue alongside balanced solid diet"
    
    def _get_breastfeeding_challenges(self, weeks: int) -> List[str]:
        """Get common breastfeeding challenges by stage"""
        if weeks < 2:
            return ["Establishing latch", "Milk supply regulation", "Nipple soreness"]
        elif weeks < 8:
            return ["Growth spurts", "Cluster feeding", "Sleep deprivation"]
        else:
            return ["Teething effects", "Distracted feeding", "Supply maintenance"]
    
    def _get_breastfeeding_support(self) -> List[str]:
        """Get breastfeeding support resources"""
        return [
            "Consult lactation consultant if needed",
            "Join breastfeeding support groups",
            "Contact La Leche League for guidance",
            "Speak with pediatrician about concerns"
        ]
    
    def _calculate_ovulation_delay(self, breastfeeding_months: float) -> str:
        """Calculate potential ovulation delay from breastfeeding"""
        delay = breastfeeding_months * 0.5
        return f"Ovulation may be delayed by approximately {delay:.1f} months due to breastfeeding"
    
    def _address_feeding_difficulties(self, difficulties: List[str]) -> Dict[str, List[str]]:
        """Address specific feeding difficulties"""
        solutions = {}
        
        for difficulty in difficulties:
            if "latch" in difficulty.lower():
                solutions["latch_issues"] = [
                    "Try different breastfeeding positions",
                    "Ensure baby's mouth covers areola",
                    "Consult lactation consultant"
                ]
            elif "supply" in difficulty.lower():
                solutions["supply_concerns"] = [
                    "Feed more frequently",
                    "Stay well hydrated",
                    "Consider pumping between feeds"
                ]
            elif "pain" in difficulty.lower():
                solutions["pain_management"] = [
                    "Check baby's latch",
                    "Use lanolin cream for nipples",
                    "Seek professional help if pain persists"
                ]
        
        return solutions

def postpartum_tracker_tool(data: str) -> str:
    """Main function for postpartum tracker tool"""
    try:
        # Parse input data
        input_data = json.loads(data)
        action = input_data.get("action")
        
        # Initialize tool
        tool = PostpartumTrackerTool()
        
        # Route to appropriate method
        if action == "track_recovery":
            result = tool.track_recovery(input_data.get("recovery_data", {}))
        elif action == "track_baby_development":
            baby_data = input_data.get("baby_data", {})
            weeks_since_birth = input_data.get("weeks_since_birth", 0)
            result = tool.track_baby_development(baby_data, weeks_since_birth)
        elif action == "track_breastfeeding":
            feeding_data = input_data.get("feeding_data", {})
            result = tool.track_breastfeeding(feeding_data)
        else:
            result = {"error": f"Unknown action: {action}"}
        
        # Add metadata
        result["timestamp"] = datetime.datetime.now().isoformat()
        result["action"] = action
        
        return json.dumps(result, indent=2)
        
    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error in postpartum tracker tool: {str(e)}")
        return json.dumps({"error": "Invalid JSON input"})
    except Exception as e:
        logger.error(f"Error in postpartum tracker tool: {str(e)}")
        return json.dumps({"error": f"Failed to process postpartum tracking request: {str(e)}"})

# Tool metadata for agent integration
TOOL_METADATA = {
    "name": "postpartum_tracker_tool",
    "description": "Comprehensive postpartum recovery and baby care tracking with health monitoring and recommendations",
    "actions": {
        "track_recovery": "Track maternal postpartum recovery progress",
        "track_baby_development": "Monitor baby development and milestones",
        "track_breastfeeding": "Track breastfeeding progress and provide support"
    }
}
