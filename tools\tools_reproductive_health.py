import json
import pandas as pd
import numpy as np
import datetime
from typing import Dict, Any
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from statsmodels.tsa.arima.model import ARIMA

# --- File Constants ---
CYCLE_FILE = "user_data.json"
ACTIVITY_FILE = "activity_data.json"
POSTPARTUM_LOG = "postpartum_logs.json"

# --- Utility ---
def load_json(file):
    try:
        with open(file, "r") as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_json(file, data):
    with open(file, "w") as f:
        json.dump(data, f, indent=4)

# --- Menstrual Cycle Tracker ---
def add_cycle_data(user, payload):
    data = load_json(CYCLE_FILE)
    cycles = data.setdefault(user, {}).setdefault("cycle_data", [])
    new_entry = {
        "start_date": payload["start_date"],
        "period_duration": payload["period_duration"],
        "luteal_phase": payload["luteal_phase"],
        "stress": payload["stress"],
        "exercise": payload["exercise"],
        "sleep": payload["sleep"],
        "weight_change": payload["weight_change"]
    }
    cycles.append(new_entry)
    data[user]["cycle_data"] = sort_and_recalculate_cycles(cycles)
    save_json(CYCLE_FILE, data)
    return data[user]["cycle_data"]

def sort_and_recalculate_cycles(cycles):
    df = pd.DataFrame(cycles)
    df["start_date"] = pd.to_datetime(df["start_date"])
    df = df.sort_values("start_date")
    cycle_lengths = [28]
    for i in range(1, len(df)):
        cycle_lengths.append((df.iloc[i]["start_date"] - df.iloc[i - 1]["start_date"]).days)
    df["cycle_length"] = cycle_lengths
    df["end_date"] = df["start_date"] + pd.to_timedelta(df["period_duration"], unit='D')
    df["start_date"] = df["start_date"].dt.strftime('%Y-%m-%d')
    df["end_date"] = df["end_date"].dt.strftime('%Y-%m-%d')
    return df.to_dict(orient="records")

def predict_next_cycle(user):
    data = load_json(CYCLE_FILE).get(user, {}).get("cycle_data", [])
    if len(data) < 3:
        return {"warning": "Not enough data for prediction"}
    df = pd.DataFrame(data)
    df["start_date"] = pd.to_datetime(df["start_date"])
    df = df.sort_values("start_date")
    ts = df["cycle_length"].astype(float)
    train = ts[:-1]
    last = df.iloc[-1]["start_date"]
    model = ARIMA(train, order=(1,1,1)).fit()
    next_cycle_len = round(model.forecast()[0])
    next_start = last + pd.Timedelta(days=next_cycle_len)
    ovulation = next_start - pd.Timedelta(days=14)
    window = f"{(ovulation - pd.Timedelta(days=2)).strftime('%Y-%m-%d')} to {(ovulation + pd.Timedelta(days=2)).strftime('%Y-%m-%d')}"
    return {
        "Predicted Cycle Length": next_cycle_len,
        "Next Period Start": next_start.strftime('%Y-%m-%d'),
        "Ovulation Window": window
    }

# --- Pregnancy Monitoring ---
def calculate_gestational_age(lmp_date):
    today = datetime.date.today()
    delta = (today - lmp_date).days
    return delta // 7, delta % 7

def predict_diagnosis(symptoms, weeks):
    trimester = "First" if weeks <= 12 else "Second" if weeks <= 27 else "Third"
    diagnosis = []
    if trimester == "First":
        if "Light spotting (pink or brown)" in symptoms:
            diagnosis.append("Implantation Bleeding")
    if trimester == "Third":
        if "Painless, bright red bleeding" in symptoms:
            diagnosis.append("Placenta Previa")
    return diagnosis or ["No critical symptoms detected"]

def expected_delivery(lmp_date):
    return {
        "Start": (lmp_date + datetime.timedelta(weeks=37)).strftime('%Y-%m-%d'),
        "End": (lmp_date + datetime.timedelta(weeks=42)).strftime('%Y-%m-%d')
    }

# --- Postpartum Monitoring ---
def detect_anomalies(entry):
    anomalies = []
    mother = entry.get("mother", {})
    baby = entry.get("baby", {})

    # Mood and Sleep
    if mother.get("mood") in ["sad", "anxious"] and mother.get("sleep_hours", 0) < 4:
        anomalies.append("Mother is low on mood with less than 4 hours of sleep...")

    # Pain level
    if mother.get("pain_level", 0) >= 6 and entry.get("days_since_delivery", 0) > 14:
        anomalies.append("High pain level after 2 weeks postpartum")

    # Baby feeding frequency
    if baby.get("feeding_frequency", 0) < 6:
        anomalies.append("Baby feeding frequency is below recommended level")

    # Baby urination
    if not baby.get("urinates", True):
        anomalies.append("Baby has not passed urine today")

    # Wound notes
    wound_notes = mother.get("wound_notes", "")
    if "redness" in wound_notes.lower():
        anomalies.append("Wound note indicates redness (possible infection)")

    # Mood log check
    mood_log = mother.get("mood_log", [])
    if mood_log.count("sad") + mood_log.count("anxious") >= 3:
        anomalies.append("Consistent low mood signs - possible postpartum depression")

    return anomalies


def track_postpartum_cycle(breastfeeding_months):
    return f"Ovulation may delay by approx. {breastfeeding_months * 0.5:.1f} months"

# --- Controller ---
def run_reproductive_agent(user_id: str, mode: str, payload: Dict[str, Any]) -> Dict[str, Any]:
    if mode == "cycle":
        cycles = add_cycle_data(user_id, payload)
        prediction = predict_next_cycle(user_id)
        return {
            "mode": "Cycle Tracking",
            "latest_cycle": cycles[-1],
            "next_prediction": prediction
        }

    elif mode == "pregnancy":
        lmp = datetime.datetime.strptime(payload["lmp_date"], "%Y-%m-%d").date()
        weeks, days = calculate_gestational_age(lmp)
        diag = predict_diagnosis(payload.get("symptoms", []), weeks)
        edd = expected_delivery(lmp)
        return {
            "mode": "Pregnancy Monitoring",
            "Gestational Age": f"{weeks} weeks {days} days",
            "Expected Delivery Window": edd,
            "Diagnosis": diag
        }

    elif mode == "postpartum":
        delivery = datetime.datetime.strptime(payload["delivery_date"], "%Y-%m-%d").date()
        days = (datetime.date.today() - delivery).days
        payload["days_since_delivery"] = days  # Inject for anomaly detection
    
        anomalies = detect_anomalies(payload)
        ovulation = track_postpartum_cycle(payload["breastfeeding_duration"])
    
        return {
            "mode": "Postpartum Recovery",
            "Days Since Delivery": days,
            "Flags": anomalies,
            "Ovulation Info": ovulation
        }

