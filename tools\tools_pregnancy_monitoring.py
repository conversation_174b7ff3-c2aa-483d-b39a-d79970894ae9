"""
Pregnancy Monitoring Tool
Handles pregnancy tracking, symptom assessment, contraction timing, and fetal movement counting
"""

import json
import logging
import datetime
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class PregnancyMonitoringTool:
    """Tool for comprehensive pregnancy monitoring"""
    
    def __init__(self):
        self.symptoms_list = [
            "Light spotting (pink or brown)", "Mild cramping",
            "Moderate cramps or back pain with bleeding", "Heavy bleeding with clots + strong cramps",
            "Sharp, stabbing pain on one side + dizziness", "Painless, bright red bleeding",
            "Severe, constant abdominal pain + bleeding", "Decreased fetal movements",
            "Bloody show (mucus mixed with blood)", "Severe headaches + swelling or vision changes"
        ]
        
        self.checklists = {
            "Nursery Preparation": [
                "Choose a room for the nursery", "Paint and decorate the nursery",
                "Set up crib or bassinet", "Install baby monitor", "Install blackout curtains",
                "Assemble changing table and storage", "Organize baby clothes by size"
            ],
            "Baby Items Shopping": [
                "Newborn onesies (5–7)", "Sleepers or footed pajamas (4–6)",
                "Socks or booties (4 pairs)", "Hats (2–3)", "Swaddles and receiving blankets",
                "Bottles and nipples (if bottle feeding)", "Breast pump (if breastfeeding)",
                "Nursing bras and pads", "Burp cloths (6+)", "Newborn diapers (2–3 packs)"
            ],
            "Hospital Bag": [
                "ID, insurance, birth plan", "Comfortable clothing and robe", "Nursing bras/tanks",
                "Maternity underwear", "Toiletries (toothbrush, shampoo, lip balm)",
                "Phone & charger", "Snacks and water bottle", "Slippers or flip flops",
                "Newborn outfit (2–3)", "Car seat (for discharge)"
            ]
        }
    
    def calculate_gestational_age(self, lmp_date: str) -> Dict[str, Any]:
        """Calculate gestational age from last menstrual period"""
        try:
            lmp = datetime.datetime.strptime(lmp_date, "%Y-%m-%d").date()
            today = datetime.date.today()
            delta = (today - lmp).days
            weeks = delta // 7
            days = delta % 7
            
            # Determine trimester
            if weeks <= 12:
                trimester = "First Trimester"
                progress = (weeks / 12) * 100
            elif weeks <= 27:
                trimester = "Second Trimester"
                progress = ((weeks - 13) / (27 - 13)) * 100
            else:
                trimester = "Third Trimester"
                progress = ((weeks - 28) / (40 - 28)) * 100
            
            # Calculate expected delivery date
            edd = lmp + datetime.timedelta(weeks=40)
            
            return {
                "weeks": weeks,
                "days": days,
                "trimester": trimester,
                "progress_percent": min(100, max(0, int(progress))),
                "expected_delivery_date": edd.strftime("%Y-%m-%d"),
                "conception_date": (lmp + datetime.timedelta(days=14)).strftime("%Y-%m-%d")
            }
            
        except ValueError as e:
            logger.error(f"Invalid date format: {e}")
            return {"error": "Invalid date format. Please use YYYY-MM-DD"}
        except Exception as e:
            logger.error(f"Error calculating gestational age: {e}")
            return {"error": f"Failed to calculate gestational age: {str(e)}"}
    
    def assess_symptoms(self, symptoms: List[str], gestational_age: Dict) -> Dict[str, Any]:
        """Assess pregnancy symptoms and provide risk analysis"""
        try:
            weeks = gestational_age.get("weeks", 0)
            trimester = gestational_age.get("trimester", "Unknown")
            
            # Risk assessment by trimester
            risk_patterns = {
                "First Trimester": {
                    "Light spotting (pink or brown)": "Implantation Bleeding (Low Risk - Normal in early pregnancy)",
                    "Mild cramping": "Implantation Bleeding (Low Risk - Normal in early pregnancy)",
                    "Moderate cramps or back pain with bleeding": "Threatened Miscarriage (Moderate Risk - Needs monitoring & rest)",
                    "Heavy bleeding with clots + strong cramps": "Miscarriage (High Risk - Urgent medical attention required)",
                    "Sharp, stabbing pain on one side + dizziness": "Ectopic Pregnancy (Critical Risk - Immediate medical attention required)",
                },
                "Second Trimester": {
                    "Moderate cramps or back pain with bleeding": "Possible Cervical Insufficiency (Moderate Risk - Needs medical review)",
                    "Painless, bright red bleeding": "Placenta Previa (High Risk - Requires hospital monitoring)",
                    "Severe, constant abdominal pain + bleeding": "Placental Abruption (Critical Risk - Immediate medical help needed)",
                },
                "Third Trimester": {
                    "Bloody show (mucus mixed with blood)": "Labor-Related Bleeding (Low Risk - Normal before labor)",
                    "Painless, bright red bleeding": "Placenta Previa (High Risk - Requires hospital monitoring)",
                    "Severe, constant abdominal pain + bleeding": "Placental Abruption (Critical Risk - Immediate medical help needed)",
                    "Decreased fetal movements": "Potential Fetal Distress (Critical Risk - Seek immediate care)",
                    "Severe headaches + swelling or vision changes": "Possible Preeclampsia (Critical Risk - Contact doctor immediately)",
                }
            }
            
            assessments = []
            risk_level = "Low"
            
            for symptom in symptoms:
                if symptom in risk_patterns.get(trimester, {}):
                    assessment = risk_patterns[trimester][symptom]
                    assessments.append(assessment)
                    
                    # Determine overall risk level
                    if "Critical Risk" in assessment:
                        risk_level = "Critical"
                    elif "High Risk" in assessment and risk_level != "Critical":
                        risk_level = "High"
                    elif "Moderate Risk" in assessment and risk_level not in ["Critical", "High"]:
                        risk_level = "Moderate"
                else:
                    assessments.append(f"Symptom '{symptom}' not typical for {trimester}")
            
            if not assessments:
                assessments = ["No concerning symptoms detected for current trimester"]
            
            return {
                "symptoms_assessed": symptoms,
                "risk_level": risk_level,
                "assessments": assessments,
                "trimester": trimester,
                "recommendations": self._get_symptom_recommendations(risk_level)
            }
            
        except Exception as e:
            logger.error(f"Error assessing symptoms: {e}")
            return {"error": f"Failed to assess symptoms: {str(e)}"}
    
    def get_milestones(self, gestational_age: Dict) -> Dict[str, Any]:
        """Get pregnancy milestones and upcoming appointments"""
        try:
            weeks = gestational_age.get("weeks", 0)
            milestones = []
            upcoming = []
            
            # Current milestones
            if 8 <= weeks <= 12:
                milestones.append("✅ Time for First Ultrasound (Week 8–12)")
            elif weeks < 8:
                upcoming.append("First Ultrasound (Week 8–12)")
            
            if 18 <= weeks <= 22:
                milestones.append("🎉 Time for Gender Reveal Scan (Week 18–22)")
            elif weeks < 18:
                upcoming.append("Gender Reveal Scan (Week 18–22)")
            
            if 24 <= weeks <= 28:
                milestones.append("🩺 Glucose Screening Test (Week 24–28)")
            elif weeks < 24:
                upcoming.append("Glucose Screening Test (Week 24–28)")
            
            if weeks >= 36:
                milestones.append("👶 Full-term pregnancy approaching - prepare for delivery")
            elif weeks >= 32:
                upcoming.append("Full-term milestone (Week 37)")
            
            return {
                "current_milestones": milestones,
                "upcoming_milestones": upcoming,
                "weeks": weeks
            }
            
        except Exception as e:
            logger.error(f"Error getting milestones: {e}")
            return {"error": f"Failed to get milestones: {str(e)}"}
    
    def track_fetal_movements(self, kick_data: Dict) -> Dict[str, Any]:
        """Track and analyze fetal movement patterns"""
        try:
            kicks_per_hour = kick_data.get("kicks_per_hour", 0)
            tracking_duration = kick_data.get("duration_minutes", 60)
            gestational_weeks = kick_data.get("gestational_weeks", 20)
            
            # Normal kick patterns vary by gestational age
            if gestational_weeks < 20:
                expected_range = "Movements may not be consistently felt yet"
                concern_threshold = 0
            elif gestational_weeks < 28:
                expected_range = "10-15 movements per 2 hours"
                concern_threshold = 5
            else:
                expected_range = "10+ movements per 2 hours"
                concern_threshold = 10
            
            # Assess kick count
            if gestational_weeks >= 20:
                if kicks_per_hour < concern_threshold / 2:
                    status = "Concerning - Contact healthcare provider"
                    recommendation = "Decreased fetal movement requires immediate medical attention"
                elif kicks_per_hour < concern_threshold:
                    status = "Monitor closely"
                    recommendation = "Continue monitoring. If movements don't increase, contact your doctor"
                else:
                    status = "Normal activity"
                    recommendation = "Fetal movements appear normal for gestational age"
            else:
                status = "Early pregnancy"
                recommendation = "Consistent movement tracking typically starts around week 20"
            
            return {
                "kicks_recorded": kicks_per_hour,
                "tracking_duration": tracking_duration,
                "expected_range": expected_range,
                "status": status,
                "recommendation": recommendation,
                "gestational_weeks": gestational_weeks
            }
            
        except Exception as e:
            logger.error(f"Error tracking fetal movements: {e}")
            return {"error": f"Failed to track fetal movements: {str(e)}"}
    
    def analyze_contractions(self, contraction_data: List[Dict]) -> Dict[str, Any]:
        """Analyze contraction patterns for labor assessment"""
        try:
            if not contraction_data:
                return {"message": "No contraction data to analyze"}
            
            # Calculate intervals between contractions
            intervals = []
            durations = []
            
            for i, contraction in enumerate(contraction_data):
                duration = contraction.get("duration_seconds", 0)
                durations.append(duration)
                
                if i > 0:
                    prev_time = datetime.datetime.fromisoformat(contraction_data[i-1]["start_time"])
                    curr_time = datetime.datetime.fromisoformat(contraction["start_time"])
                    interval = (curr_time - prev_time).total_seconds() / 60  # minutes
                    intervals.append(interval)
            
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                avg_duration = sum(durations) / len(durations)
                
                # Labor assessment
                if avg_interval <= 5 and avg_duration >= 60:
                    labor_status = "Active Labor - Go to hospital immediately"
                    urgency = "Critical"
                elif avg_interval <= 10 and avg_duration >= 45:
                    labor_status = "Early Labor - Prepare for hospital"
                    urgency = "High"
                elif avg_interval <= 20:
                    labor_status = "Pre-labor contractions - Monitor closely"
                    urgency = "Moderate"
                else:
                    labor_status = "Irregular contractions - Continue monitoring"
                    urgency = "Low"
            else:
                labor_status = "Single contraction recorded"
                urgency = "Low"
                avg_interval = 0
                avg_duration = durations[0] if durations else 0
            
            return {
                "total_contractions": len(contraction_data),
                "average_interval_minutes": round(avg_interval, 1) if intervals else 0,
                "average_duration_seconds": round(avg_duration, 1),
                "labor_status": labor_status,
                "urgency_level": urgency,
                "recommendation": self._get_contraction_recommendations(urgency)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing contractions: {e}")
            return {"error": f"Failed to analyze contractions: {str(e)}"}
    
    def _get_symptom_recommendations(self, risk_level: str) -> List[str]:
        """Get recommendations based on symptom risk level"""
        if risk_level == "Critical":
            return [
                "Seek immediate medical attention",
                "Go to emergency room or call emergency services",
                "Do not delay medical care"
            ]
        elif risk_level == "High":
            return [
                "Contact your healthcare provider immediately",
                "Monitor symptoms closely",
                "Prepare to go to hospital if symptoms worsen"
            ]
        elif risk_level == "Moderate":
            return [
                "Schedule appointment with healthcare provider",
                "Monitor symptoms and note any changes",
                "Rest and avoid strenuous activities"
            ]
        else:
            return [
                "Continue regular prenatal care",
                "Monitor for any changes in symptoms",
                "Maintain healthy pregnancy habits"
            ]
    
    def _get_contraction_recommendations(self, urgency: str) -> List[str]:
        """Get recommendations based on contraction urgency"""
        if urgency == "Critical":
            return [
                "Go to hospital immediately",
                "Call your healthcare provider",
                "Bring hospital bag and birth plan"
            ]
        elif urgency == "High":
            return [
                "Prepare for hospital departure",
                "Contact your healthcare provider",
                "Gather hospital essentials"
            ]
        elif urgency == "Moderate":
            return [
                "Continue timing contractions",
                "Rest and stay hydrated",
                "Alert your birth partner"
            ]
        else:
            return [
                "Continue monitoring",
                "Practice relaxation techniques",
                "Stay comfortable and hydrated"
            ]

def pregnancy_monitoring_tool(data: str) -> str:
    """Main function for pregnancy monitoring tool"""
    try:
        # Parse input data
        input_data = json.loads(data)
        action = input_data.get("action")
        
        # Initialize tool
        tool = PregnancyMonitoringTool()
        
        # Route to appropriate method
        if action == "calculate_gestational_age":
            result = tool.calculate_gestational_age(input_data.get("lmp_date"))
        elif action == "assess_symptoms":
            gestational_age = input_data.get("gestational_age", {})
            symptoms = input_data.get("symptoms", [])
            result = tool.assess_symptoms(symptoms, gestational_age)
        elif action == "get_milestones":
            gestational_age = input_data.get("gestational_age", {})
            result = tool.get_milestones(gestational_age)
        elif action == "track_fetal_movements":
            kick_data = input_data.get("kick_data", {})
            result = tool.track_fetal_movements(kick_data)
        elif action == "analyze_contractions":
            contraction_data = input_data.get("contraction_data", [])
            result = tool.analyze_contractions(contraction_data)
        else:
            result = {"error": f"Unknown action: {action}"}
        
        # Add metadata
        result["timestamp"] = datetime.datetime.now().isoformat()
        result["action"] = action
        
        return json.dumps(result, indent=2)
        
    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error in pregnancy monitoring tool: {str(e)}")
        return json.dumps({"error": "Invalid JSON input"})
    except Exception as e:
        logger.error(f"Error in pregnancy monitoring tool: {str(e)}")
        return json.dumps({"error": f"Failed to process pregnancy monitoring request: {str(e)}"})

# Tool metadata for agent integration
TOOL_METADATA = {
    "name": "pregnancy_monitoring_tool",
    "description": "Comprehensive pregnancy monitoring including gestational age calculation, symptom assessment, and labor tracking",
    "actions": {
        "calculate_gestational_age": "Calculate weeks and trimester from LMP date",
        "assess_symptoms": "Assess pregnancy symptoms and risk levels",
        "get_milestones": "Get pregnancy milestones and upcoming appointments",
        "track_fetal_movements": "Track and analyze fetal movement patterns",
        "analyze_contractions": "Analyze contraction patterns for labor assessment"
    }
}
