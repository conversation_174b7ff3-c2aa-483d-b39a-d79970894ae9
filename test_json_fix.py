#!/usr/bin/env python3
"""
Test script to verify that the JSON cleaning middleware fixes the non-breaking space issue
"""

import requests
import json

# Test data that mimics the problematic request from the log
test_data = {
    "user_id": "test_user_001",
    "assessment_data": {
        "age": 28,
        "gender": "Female",
        "country": "Canada",
        "recent_stress_event": False,
        "stress_responses": {
            "work": [2, 1, 2, 2, 1, 4, 2, 1, 4, 4]
        },
        "phq9_responses": [0, 1, 1, 1, 0, 0, 1, 0, 0],
        "gad7_responses": [1, 0, 1, 1, 0, 1, 0]  # This will be corrupted with non-breaking spaces
    }
}

def test_clean_json():
    """Test that the server can handle JSON with non-breaking spaces"""
    
    # Convert to JSON string
    json_str = json.dumps(test_data, indent=2)
    
    # Introduce non-breaking spaces (the problematic characters from the log)
    corrupted_json = json_str.replace('"gad7_responses": [1, 0, 1, 1, 0, 1, 0]',
                                     '"gad7_responses": [1, 0, 1, 1,\u00a00,\u00a01,\u00a00]')

    # Also add some at the end to match the log exactly
    corrupted_json = corrupted_json.replace('  }\n}', '\u00a0\u00a0}\n}')
    
    print("Original JSON (last few lines):")
    print(json_str.split('\n')[-5:])
    
    print("\nCorrupted JSON (last few lines):")
    print(corrupted_json.split('\n')[-5:])
    
    # Try to parse the corrupted JSON - this should fail
    try:
        json.loads(corrupted_json)
        print("\n❌ ERROR: Corrupted JSON should not parse successfully!")
    except json.JSONDecodeError as e:
        print(f"\n✅ Expected: Corrupted JSON fails to parse: {e}")
    
    # Test the server endpoint (running on localhost:8002)
    server_url = "http://localhost:8002/mental-health-assessment"
    
    try:
        # Send the corrupted JSON to the server
        response = requests.post(
            server_url,
            data=corrupted_json.encode('utf-8'),
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            print(f"\n✅ SUCCESS: Server handled corrupted JSON successfully!")
            print(f"Response: {response.json()}")
        else:
            print(f"\n⚠️  Server returned status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"\n⚠️  Could not connect to server at {server_url}")
        print("Make sure the server is running with: python agent_server.py")
    except Exception as e:
        print(f"\n❌ Error testing server: {e}")

if __name__ == "__main__":
    test_clean_json()
